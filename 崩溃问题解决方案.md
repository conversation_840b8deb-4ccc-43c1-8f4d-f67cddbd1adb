# Unity6.2 项目崩溃问题解决方案

## 已修复的主要问题

### 1. 线程安全问题
**问题**: `Update()` 函数和 `Gamethread::_processQueue()` 之间存在线程竞争
**解决方案**: 
- 在 `Update()` 函数中添加了 `std::mutex` 保护
- 在 `Thread.cpp` 中改进了队列处理的线程安全性
- 使用 `std::move` 减少锁定时间

### 2. UnityResolve 初始化问题
**问题**: 缺少适当的线程管理和错误检查
**解决方案**:
- 在 `Run()` 函数中添加了 GameAssembly.dll 存在性检查
- 改进了 UnityResolve 初始化的错误处理
- 添加了详细的日志输出

### 3. Helper.hpp 中的内存访问问题
**问题**: `GetMonoBehaviour()` 函数可能访问无效内存
**解决方案**:
- 添加了 `UnityResolve::ThreadAttach()` 和 `ThreadDetach()` 调用
- 限制了检查的 GameObject 数量（最多10个）
- 添加了详细的异常处理

### 4. ESP 功能的稳定性问题
**问题**: `UpdateGameObjectInfos()` 和 `DrawGameObjects()` 缺少错误处理
**解决方案**:
- 添加了屏幕坐标有效性检查
- 改进了线程安全的对象访问
- 添加了详细的调试输出

## 使用说明

### 编译项目
1. 运行 `test_build.bat` 来编译项目
2. 确保你有 Visual Studio 2019 或 2022 安装

### 调试崩溃
1. 查看控制台输出中的错误信息
2. 检查 `Logs.txt` 文件中的日志
3. 使用 Visual Studio 调试器附加到目标进程

### 常见崩溃原因和解决方法

#### 1. GameAssembly.dll 未找到
**症状**: 程序启动时立即崩溃
**解决方案**: 确保目标 Unity 游戏正在运行

#### 2. UnityResolve 初始化失败
**症状**: 控制台显示 "Failed to initialize UnityResolve"
**解决方案**: 
- 检查目标游戏是否使用 IL2CPP
- 确保 DLL 注入成功

#### 3. 线程死锁
**症状**: 程序挂起，无响应
**解决方案**: 
- 检查是否有多个线程同时访问 Unity API
- 确保所有 mutex 都正确释放

#### 4. 内存访问违规
**症状**: 访问违规异常
**解决方案**:
- 检查指针有效性
- 使用 try-catch 包装所有 Unity API 调用

## 性能优化建议

1. **减少 GameObject 遍历频率**: 不要在每帧都调用 `FindObjectsByType`
2. **使用对象池**: 重用 GameObjectInfo 对象
3. **限制绘制数量**: 只绘制屏幕可见的对象
4. **异步处理**: 将耗时操作移到后台线程

## 调试技巧

### 启用详细日志
在 `main.cpp` 中设置详细日志级别：
```cpp
#define VERBOSE_LOGGING 1
```

### 使用 Visual Studio 调试器
1. 编译 Debug 版本
2. 附加到目标进程
3. 在可能崩溃的地方设置断点

### 内存泄漏检测
使用 Application Verifier 或 CRT 调试堆来检测内存问题

## 已知限制

1. 某些 Unity 版本可能不兼容
2. 反作弊系统可能检测到 DLL 注入
3. 游戏更新可能破坏兼容性

## 下一步改进

1. 添加配置文件支持
2. 实现更好的错误恢复机制
3. 添加自动重连功能
4. 优化内存使用
