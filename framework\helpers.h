#pragma once

#include <string>
#include <sstream>
#include <iomanip>

uintptr_t il2cppi_get_base_address();

// Helper function to append text to a file
void il2cppi_log_write(std::string text);

// Helper function to open a new console window and redirect stdout there
void SetConsole();

#if _MSC_VER >= 1920
//// Helper function to convert Il2CppString to std::string
//std::string il2cppi_to_string(Il2CppString* str);
//
//// Helper function to convert System.String to std::string
//std::string il2cppi_to_string(app::String* str);
#endif

template<typename T> bool il2cppi_is_initialized(T* metadataItem) {
#if __IL2CPP_METADATA_VERISON < 270
    return *metadataItem != 0;
#else
    // Metadata >=27 (Unity 2020.2)
    return !((uintptr_t) *metadataItem & 1);
#endif
}

template<typename T> std::string to_hex_string(T i) {
    std::stringstream stream;
    stream << "0x" << std::setfill('0') << std::setw(sizeof(T) * 2) << std::hex << i;
    return stream.str();
}