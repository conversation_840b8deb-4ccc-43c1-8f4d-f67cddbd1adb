<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <VCProjectVersion>17.0</VCProjectVersion>
    <Keyword>Win32Proj</Keyword>
    <ProjectGuid>{5b0ae60e-ec94-4b7f-962d-7c671a2f8038}</ProjectGuid>
    <RootNamespace>Unity62</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <OutDir>..\Build\</OutDir>
    <IntDir>..\Build\Temp\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <OutDir>..\Build\</OutDir>
    <IntDir>..\Build\Temp\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>WIN32;_DEBUG;UNITY62_EXPORTS;_WINDOWS;_USRDLL;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableUAC>false</EnableUAC>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>WIN32;NDEBUG;UNITY62_EXPORTS;_WINDOWS;_USRDLL;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableUAC>false</EnableUAC>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>TurnOffAllWarnings</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_DEBUG;UNITY62_EXPORTS;_WINDOWS;_USRDLL;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <LanguageStandard_C>stdclatest</LanguageStandard_C>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <DisableSpecificWarnings>4996</DisableSpecificWarnings>
      <AdditionalIncludeDirectories>$(ProjectDir)appdata;$(ProjectDir)framework;$(ProjectDir)user;$(ProjectDir)libraries</AdditionalIncludeDirectories>
      <AdditionalOptions>/Zc:char8_t-</AdditionalOptions>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableUAC>false</EnableUAC>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>TurnOffAllWarnings</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>NDEBUG;UNITY62_EXPORTS;_WINDOWS;_USRDLL;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <LanguageStandard_C>stdclatest</LanguageStandard_C>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <DisableSpecificWarnings>4996</DisableSpecificWarnings>
      <AdditionalIncludeDirectories>$(ProjectDir)appdata;$(ProjectDir)framework;$(ProjectDir)user;$(ProjectDir)libraries</AdditionalIncludeDirectories>
      <AdditionalOptions>/Zc:char8_t-</AdditionalOptions>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableUAC>false</EnableUAC>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="framework\dllmain.cpp" />
    <ClCompile Include="framework\helpers.cpp" />
    <ClCompile Include="libraries\detours\detours.cpp" />
    <ClCompile Include="libraries\detours\disasm.cpp" />
    <ClCompile Include="libraries\detours\modules.cpp" />
    <ClCompile Include="libraries\imgui\imgui.cpp" />
    <ClCompile Include="libraries\imgui\imgui_draw.cpp" />
    <ClCompile Include="libraries\imgui\imgui_impl_dx11.cpp" />
    <ClCompile Include="libraries\imgui\imgui_impl_win32.cpp" />
    <ClCompile Include="libraries\imgui\imgui_tables.cpp" />
    <ClCompile Include="libraries\imgui\imgui_widgets.cpp" />
    <ClCompile Include="libraries\pipeline\Dx11.cpp" />
    <ClCompile Include="libraries\pipeline\gui\GUITheme.cpp" />
    <ClCompile Include="libraries\pipeline\gui\menu.cpp" />
    <ClCompile Include="libraries\pipeline\gui\tabs\SettingsTAB.cpp" />
    <!-- <ClCompile Include="libraries\pipeline\hooks\DirectX.cpp" /> -->
    <!-- <ClCompile Include="libraries\pipeline\hooks\InitHooks.cpp" /> -->
    <ClCompile Include="libraries\pipeline\keybinds.cpp" />
    <ClCompile Include="libraries\pipeline\settings.cpp" />
    <ClCompile Include="libraries\Thread.cpp" />
    <!-- <ClCompile Include="user\Esp.cpp" /> -->
    <ClCompile Include="user\main_safe.cpp" />
    <!-- <ClCompile Include="user\main.cpp" /> -->
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="framework\helpers.h" />
    <ClInclude Include="libraries\Callback.hpp" />
    <ClInclude Include="libraries\Data.hpp" />
    <ClInclude Include="libraries\Defines.hpp" />
    <ClInclude Include="libraries\detours\detours.h" />
    <ClInclude Include="libraries\Helper.hpp" />
    <ClInclude Include="libraries\imgui\imconfig.h" />
    <ClInclude Include="libraries\imgui\imgui.h" />
    <ClInclude Include="libraries\imgui\imgui_impl_dx11.h" />
    <ClInclude Include="libraries\imgui\imgui_impl_win32.h" />
    <ClInclude Include="libraries\imgui\imgui_internal.h" />
    <ClInclude Include="libraries\imgui\imstb_rectpack.h" />
    <ClInclude Include="libraries\imgui\imstb_textedit.h" />
    <ClInclude Include="libraries\imgui\imstb_truetype.h" />
    <ClInclude Include="libraries\pipeline\Dx11.h" />
    <ClInclude Include="libraries\pipeline\gui\GUITheme.h" />
    <ClInclude Include="libraries\pipeline\gui\menu.h" />
    <ClInclude Include="libraries\pipeline\gui\tabs\SettingsTAB.h" />
    <ClInclude Include="libraries\pipeline\hooks\DirectX.h" />
    <ClInclude Include="libraries\pipeline\hooks\InitHooks.h" />
    <ClInclude Include="libraries\pipeline\keybinds.h" />
    <ClInclude Include="libraries\pipeline\settings.h" />
    <ClInclude Include="libraries\Thread.h" />
    <ClInclude Include="libraries\Thread.hpp" />
    <ClInclude Include="libraries\UnityResolve.hpp" />
    <ClInclude Include="libraries\VTable.hpp" />
    <ClInclude Include="user\Esp.h" />
    <ClInclude Include="user\main.h" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>