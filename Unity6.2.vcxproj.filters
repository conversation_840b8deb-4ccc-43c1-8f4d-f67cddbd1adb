﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="源文件">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;c++;cppm;ixx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="头文件">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;h++;hm;inl;inc;ipp;xsd</Extensions>
    </Filter>
    <Filter Include="资源文件">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="framework\dllmain.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="framework\helpers.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="libraries\detours\detours.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="libraries\detours\disasm.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="libraries\detours\modules.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="libraries\imgui\imgui.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="libraries\imgui\imgui_draw.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="libraries\imgui\imgui_impl_dx11.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="libraries\imgui\imgui_impl_win32.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="libraries\imgui\imgui_tables.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="libraries\imgui\imgui_widgets.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="libraries\pipeline\gui\tabs\SettingsTAB.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="libraries\pipeline\gui\GUITheme.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="libraries\pipeline\gui\menu.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="libraries\pipeline\hooks\DirectX.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="libraries\pipeline\hooks\InitHooks.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="libraries\pipeline\Dx11.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="libraries\pipeline\keybinds.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="libraries\pipeline\settings.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="user\main.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="user\Esp.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="libraries\Thread.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="user\main.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="framework\helpers.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="libraries\detours\detours.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="libraries\imgui\imconfig.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="libraries\imgui\imgui.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="libraries\imgui\imgui_impl_dx11.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="libraries\imgui\imgui_impl_win32.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="libraries\imgui\imgui_internal.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="libraries\imgui\imstb_rectpack.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="libraries\imgui\imstb_textedit.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="libraries\imgui\imstb_truetype.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="libraries\pipeline\gui\tabs\SettingsTAB.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="libraries\pipeline\gui\GUITheme.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="libraries\pipeline\gui\menu.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="libraries\pipeline\hooks\DirectX.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="libraries\pipeline\hooks\InitHooks.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="libraries\pipeline\Dx11.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="libraries\pipeline\keybinds.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="libraries\pipeline\settings.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="libraries\UnityResolve.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="user\Esp.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="libraries\Callback.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="libraries\Helper.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="libraries\VTable.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="libraries\Thread.h">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="libraries\Data.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="libraries\Thread.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
    <ClInclude Include="libraries\Defines.hpp">
      <Filter>头文件</Filter>
    </ClInclude>
  </ItemGroup>
</Project>