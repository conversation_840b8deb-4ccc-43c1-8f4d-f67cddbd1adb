﻿#include "Esp.h"
#include <imgui/imgui.h>
#include <vector>
#include <mutex>
#include <string>



std::vector<GameObjectInfo> g_GameObjectInfos;
std::mutex g_GameObjectInfosMutex;

void UpdateGameObjectInfos() {
    std::vector<GameObjectInfo> infos;

    auto* camera = UnityResolve::UnityType::Camera::GetMain();
    if (!camera) return;

    auto* coreModule = UnityResolve::Get("UnityEngine.CoreModule.dll");
    if (!coreModule) return;
    auto* goClass = coreModule->Get("GameObject");
    if (!goClass) return;
    auto gameObjects = goClass->FindObjectsByType<UnityResolve::UnityType::GameObject*>();
    for (auto* go : gameObjects) {
        if (!go) continue;
    auto* transform = go->GetTransform();
        if (!transform) continue;
    auto pos = transform->GetPosition();
    auto screenPos = camera->WorldToScreenPoint(pos);
    auto* nameObj = go->ToString();
    std::string name = nameObj ? nameObj->ToString() : "GameObject";
        infos.push_back({name, ImVec2(screenPos.x, screenPos.y)});
    }
    std::lock_guard<std::mutex> lock(g_GameObjectInfosMutex);
    g_GameObjectInfos = std::move(infos);
}

void DrawGameObjects() {
    std::lock_guard<std::mutex> lock(g_GameObjectInfosMutex);
    ImDrawList* draw_list = ImGui::GetBackgroundDrawList();
    for (const auto& info : g_GameObjectInfos) {
        draw_list->AddText(info.screenPos, IM_COL32(255, 255, 0, 255), info.name.c_str());
    }
}