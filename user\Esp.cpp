﻿#include "Esp.h"
#include <imgui/imgui.h>
#include <vector>
#include <mutex>
#include <string>



std::vector<GameObjectInfo> g_GameObjectInfos;
std::mutex g_GameObjectInfosMutex;

void UpdateGameObjectInfos() {
    try {
        std::vector<GameObjectInfo> infos;

        auto* camera = UnityResolve::UnityType::Camera::GetMain();
        if (!camera) {
            printf("[DEBUG] UpdateGameObjectInfos: Camera not found\n");
            return;
        }

        auto* coreModule = UnityResolve::Get("UnityEngine.CoreModule.dll");
        if (!coreModule) {
            printf("[DEBUG] UpdateGameObjectInfos: CoreModule not found\n");
            return;
        }

        auto* goClass = coreModule->Get("GameObject");
        if (!goClass) {
            printf("[DEBUG] UpdateGameObjectInfos: GameObject class not found\n");
            return;
        }

        try {
            auto gameObjects = goClass->FindObjectsByType<UnityResolve::UnityType::GameObject*>();
            printf("[DEBUG] UpdateGameObjectInfos: Found %zu GameObjects\n", gameObjects.size());

            for (auto* go : gameObjects) {
                if (!go) continue;

                try {
                    auto* transform = go->GetTransform();
                    if (!transform) continue;

                    auto pos = transform->GetPosition();
                    auto screenPos = camera->WorldToScreenPoint(pos);

                    // 检查屏幕坐标是否有效
                    if (screenPos.x < -1000 || screenPos.x > 10000 ||
                        screenPos.y < -1000 || screenPos.y > 10000) {
                        continue;
                    }

                    auto* nameObj = go->ToString();
                    std::string name = nameObj ? nameObj->ToString() : "GameObject";

                    infos.push_back({name, ImVec2(screenPos.x, screenPos.y)});
                }
                catch (...) {
                    // 跳过有问题的GameObject
                    continue;
                }
            }
        }
        catch (const std::exception& e) {
            printf("[ERROR] UpdateGameObjectInfos FindObjectsByType exception: %s\n", e.what());
            return;
        }
        catch (...) {
            printf("[ERROR] UpdateGameObjectInfos FindObjectsByType unknown exception\n");
            return;
        }

        std::lock_guard<std::mutex> lock(g_GameObjectInfosMutex);
        g_GameObjectInfos = std::move(infos);
        printf("[DEBUG] UpdateGameObjectInfos: Updated with %zu objects\n", g_GameObjectInfos.size());
    }
    catch (const std::exception& e) {
        printf("[ERROR] UpdateGameObjectInfos exception: %s\n", e.what());
    }
    catch (...) {
        printf("[ERROR] UpdateGameObjectInfos unknown exception\n");
    }
}

void DrawGameObjects() {
    try {
        std::lock_guard<std::mutex> lock(g_GameObjectInfosMutex);

        if (g_GameObjectInfos.empty()) {
            return;
        }

        ImDrawList* draw_list = ImGui::GetBackgroundDrawList();
        if (!draw_list) {
            printf("[ERROR] DrawGameObjects: draw_list is null\n");
            return;
        }

        for (const auto& info : g_GameObjectInfos) {
            try {
                // 检查屏幕坐标是否在合理范围内
                if (info.screenPos.x >= 0 && info.screenPos.x <= 3840 &&
                    info.screenPos.y >= 0 && info.screenPos.y <= 2160 &&
                    !info.name.empty()) {
                    draw_list->AddText(info.screenPos, IM_COL32(255, 255, 0, 255), info.name.c_str());
                }
            }
            catch (...) {
                // 跳过有问题的对象
                continue;
            }
        }
    }
    catch (const std::exception& e) {
        printf("[ERROR] DrawGameObjects exception: %s\n", e.what());
    }
    catch (...) {
        printf("[ERROR] DrawGameObjects unknown exception\n");
    }
}