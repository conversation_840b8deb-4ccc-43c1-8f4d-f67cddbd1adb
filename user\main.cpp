﻿#define WIN32_LEAN_AND_MEAN
#include <Windows.h>
#include <iostream>
#include <mutex>
#include "helpers.h"
#include "main.h"
#include <tlhelp32.h>

#include <pipeline/hooks/InitHooks.h>
#include <UnityResolve.hpp>
#include <Callback.hpp>
#include <Thread.h>

extern const LPCWSTR LOG_FILE = L"Logs.txt";

HMODULE hModule;
HANDLE hUnloadEvent;

typedef LONG NTSTATUS;
typedef NTSTATUS(WINAPI* pNtCreateThreadEx)(PHA<PERSON>LE ThreadHandle,ACCESS_MASK DesiredAccess,LPVOID ObjectAttributes,<PERSON>AND<PERSON> ProcessHandle,LPTHREAD_START_ROUTINE lpStartAddress,LPVOID lpParameter,BOOL CreateSuspended,DWORD StackZeroBits,DWORD SizeOfStackCommit,DWOR<PERSON> SizeOfStackReserve,LPVOID lpBytesBuffer);

typedef struct _CLIENT_ID {
    HANDLE UniqueProcess;
    HANDLE UniqueThread;
} CLIENT_ID, * PCLIENT_ID;

typedef NTSTATUS(WINAPI* pRtlCreateUserThread)(HANDLE ProcessHandle,PSECURITY_DESCRIPTOR SecurityDescriptor,BOOLEAN CreateSuspended,ULONG StackZeroBits,PULONG StackReserved,PULONG StackCommit,PVOID StartAddress,PVOID StartParameter,PHANDLE ThreadHandle,PCLIENT_ID ClientID);

void CreateThreadByNtCreateThreadEx(LPTHREAD_START_ROUTINE func, LPVOID param) {
    HMODULE hNtdll = GetModuleHandleA("ntdll.dll");
    if (!hNtdll) return;
    pNtCreateThreadEx NtCreateThreadEx = (pNtCreateThreadEx)GetProcAddress(hNtdll, "NtCreateThreadEx");
    if (!NtCreateThreadEx) return;
    HANDLE hThread = NULL;
    NTSTATUS status = NtCreateThreadEx(&hThread, 0x1FFFFF, NULL, GetCurrentProcess(), func, param, FALSE, 0, 0, 0, NULL);
    if (hThread) CloseHandle(hThread);
}

void CreateThreadByRtlCreateUserThread(LPTHREAD_START_ROUTINE func, LPVOID param) {
    HMODULE hNtdll = GetModuleHandleA("ntdll.dll");
    if (!hNtdll) return;
    pRtlCreateUserThread RtlCreateUserThread = (pRtlCreateUserThread)GetProcAddress(hNtdll, "RtlCreateUserThread");
    if (!RtlCreateUserThread) return;
    HANDLE hThread = NULL;
    CLIENT_ID cid;
    NTSTATUS status = RtlCreateUserThread(GetCurrentProcess(), NULL, FALSE, 0, NULL, NULL, func, param, &hThread, &cid);
    if (hThread) CloseHandle(hThread);
}

DWORD WINAPI UnloadWatcherThread(LPVOID lpParam)
{
    HANDLE hEvent = static_cast<HANDLE>(lpParam);

    if (WaitForSingleObject(hEvent, INFINITE) == WAIT_OBJECT_0)
    {
        std::cout << "[WARNING] Unload signal received.." << std::endl;
        DetourUninitialization();
        fclose(stdout);
        FreeConsole();
        CloseHandle(hUnloadEvent);
        FreeLibraryAndExitThread(hModule, 0);
    }
    return 0;
}

void Update() {
    try {
        // 添加线程安全检查
        static std::mutex updateMutex;
        std::lock_guard<std::mutex> lock(updateMutex);

        printf("Update called\n");

        // 安全地处理队列
        if (Gamethread::_processQueue) {
            Gamethread::_processQueue();
        }

        //static GameAPI::Instance* instance = GameAPI::Instance::GetInstance();
        //if (!instance->IsHost())
        //    UnityHelpers::NotifyEnvReset();
    }
    catch (const std::exception& e) {
        printf("[ERROR] Update exception: %s\n", e.what());
    }
    catch (...) {
        printf("[ERROR] Update unknown exception\n");
    }
}

void Run(LPVOID lpParam)
{
    try {
        std::cout << "[INFO] Starting initialization..." << std::endl;

        // 检查 GameAssembly.dll 是否存在
        HMODULE gameAssembly = GetModuleHandleA("GameAssembly.dll");
        if (!gameAssembly) {
            std::cout << "[ERROR] GameAssembly.dll not found!" << std::endl;
            return;
        }

        std::cout << "[INFO] GameAssembly.dll found at: " << gameAssembly << std::endl;

        // 初始化 UnityResolve
        if (!UnityResolve::Init(gameAssembly, UnityResolve::Mode::Il2Cpp)) {
            std::cout << "[ERROR] Failed to initialize UnityResolve!" << std::endl;
            return;
        }

        hModule = (HMODULE)lpParam;
        SetConsole();

        std::cout << "[INFO] Console initialized" << std::endl;

        // 安全地获取Unity项目信息
        try {
            std::string productName = UApplication::ProductName();
            std::string gameVersion = UApplication::GetVersion();
            std::string unityVersion = UApplication::GetUnityVersion();
            std::cout << "[Unity] Product Name: " << productName << std::endl;
            std::cout << "[Unity] Game Version: " << gameVersion << std::endl;
            std::cout << "[Unity] Unity Version: " << unityVersion << std::endl;
        }
        catch (...) {
            std::cout << "[WARNING] Failed to get Unity application info" << std::endl;
        }

        std::cout << "[INFO] Initializing Detours..." << std::endl;
        DetourInitilization();

        std::cout << "[INFO] Initializing Callbacks..." << std::endl;
        // 测试极简版本的Initialize函数
        try {
            UnityResolveLib::Callback::Initialize();
            std::cout << "[INFO] Callback initialization completed" << std::endl;
        }
        catch (const std::exception& e) {
            std::cout << "[ERROR] Callback initialization failed: " << e.what() << std::endl;
        }
        catch (...) {
            std::cout << "[ERROR] Callback initialization failed with unknown exception" << std::endl;
        }

        // 暂时保持OnUpdate::Add注释状态，因为它依赖于完整的Initialize
        // UnityResolveLib::Callback::OnUpdate::Add((void*)Update);

        hUnloadEvent = CreateEvent(NULL, FALSE, FALSE, NULL);

        if (hUnloadEvent == NULL) {
            std::cout << "[ERROR] Failed to create unload event! Error code: " << GetLastError() << std::endl;
            return;
        }

        std::cout << "[INFO] Creating unload watcher thread..." << std::endl;
        CreateThreadByRtlCreateUserThread((LPTHREAD_START_ROUTINE)UnloadWatcherThread, hUnloadEvent);

        std::cout << "[INFO] Initialization completed successfully!" << std::endl;
    }
    catch (const std::exception& e) {
        std::cout << "[FATAL ERROR] Run function exception: " << e.what() << std::endl;
    }
    catch (...) {
        std::cout << "[FATAL ERROR] Run function unknown exception" << std::endl;
    }
}


