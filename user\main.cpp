﻿#define WIN32_LEAN_AND_MEAN
#include <Windows.h>
#include <iostream>
#include "helpers.h"
#include "main.h"
#include <tlhelp32.h>

#include <pipeline/hooks/InitHooks.h>
#include <UnityResolve.hpp>
#include <Callback.hpp>
#include <Thread.h>

extern const LPCWSTR LOG_FILE = L"Logs.txt";

HMODULE hModule;
HANDLE hUnloadEvent;

typedef LONG NTSTATUS;
typedef NTSTATUS(WINAPI* pNtCreateThreadEx)(PHA<PERSON><PERSON> ThreadHandle,ACCESS_MASK DesiredAccess,LP<PERSON>ID ObjectAttributes,HANDLE ProcessHandle,LPTHREAD_START_ROUTINE lpStartAddress,LPVOID lpParameter,BOOL CreateSuspended,DWORD StackZeroBits,DWORD SizeOfStackCommit,DWORD SizeOfStackReserve,LPVOID lpBytesBuffer);

typedef struct _CLIENT_ID {
    HANDLE UniqueProcess;
    HANDLE UniqueThread;
} CLIENT_ID, * PCLIENT_ID;

typedef NTSTATUS(WINAPI* pRtlCreateUserThread)(HANDLE ProcessHandle,PSECURITY_DESCRIPTOR SecurityDescriptor,BOOLEAN CreateSuspended,ULONG StackZeroBits,PULONG StackReserved,PULONG StackCommit,PVOID StartAddress,PVOID StartParameter,PHANDLE ThreadHandle,PCLIENT_ID ClientID);

void CreateThreadByNtCreateThreadEx(LPTHREAD_START_ROUTINE func, LPVOID param) {
    HMODULE hNtdll = GetModuleHandleA("ntdll.dll");
    if (!hNtdll) return;
    pNtCreateThreadEx NtCreateThreadEx = (pNtCreateThreadEx)GetProcAddress(hNtdll, "NtCreateThreadEx");
    if (!NtCreateThreadEx) return;
    HANDLE hThread = NULL;
    NTSTATUS status = NtCreateThreadEx(&hThread, 0x1FFFFF, NULL, GetCurrentProcess(), func, param, FALSE, 0, 0, 0, NULL);
    if (hThread) CloseHandle(hThread);
}

void CreateThreadByRtlCreateUserThread(LPTHREAD_START_ROUTINE func, LPVOID param) {
    HMODULE hNtdll = GetModuleHandleA("ntdll.dll");
    if (!hNtdll) return;
    pRtlCreateUserThread RtlCreateUserThread = (pRtlCreateUserThread)GetProcAddress(hNtdll, "RtlCreateUserThread");
    if (!RtlCreateUserThread) return;
    HANDLE hThread = NULL;
    CLIENT_ID cid;
    NTSTATUS status = RtlCreateUserThread(GetCurrentProcess(), NULL, FALSE, 0, NULL, NULL, func, param, &hThread, &cid);
    if (hThread) CloseHandle(hThread);
}

DWORD WINAPI UnloadWatcherThread(LPVOID lpParam)
{
    HANDLE hEvent = static_cast<HANDLE>(lpParam);

    if (WaitForSingleObject(hEvent, INFINITE) == WAIT_OBJECT_0)
    {
        std::cout << "[WARNING] Unload signal received.." << std::endl;
        DetourUninitialization();
        fclose(stdout);
        FreeConsole();
        CloseHandle(hUnloadEvent);
        FreeLibraryAndExitThread(hModule, 0);
    }
    return 0;
}

void Update() {

    printf("1");
    Gamethread::_processQueue();

    //static GameAPI::Instance* instance = GameAPI::Instance::GetInstance();
    //if (!instance->IsHost())
    //    UnityHelpers::NotifyEnvReset();
}

void Run(LPVOID lpParam)
{
    UnityResolve::Init(GetModuleHandleA("GameAssembly.dll"), UnityResolve::Mode::Il2Cpp);

    hModule = (HMODULE)lpParam;

    SetConsole();



    // 打印Unity项目信息
    std::string productName = UApplication::ProductName();
    std::string gameVersion = UApplication::GetVersion();
    std::string unityVersion = UApplication::GetUnityVersion();
    std::cout << "[Unity] Product Name: " << productName << std::endl;
    std::cout << "[Unity] Game Version: " << gameVersion << std::endl;
    std::cout << "[Unity] Unity Version: " << unityVersion << std::endl;

    std::cout << "[INFO] Initializing.. " << std::endl;

    DetourInitilization();

    // 测试极简版本的Initialize函数
    UnityResolveLib::Callback::Initialize();

    // 暂时保持OnUpdate::Add注释状态，因为它依赖于完整的Initialize
    // UnityResolveLib::Callback::OnUpdate::Add((void*)Update);


    hUnloadEvent = CreateEvent(NULL, FALSE, FALSE, NULL);

    if (hUnloadEvent == NULL) {
        std::cout << "Failed to create unload event! Error code: " << GetLastError() << std::endl;
        return;
    }

    DWORD dwThreadId;

    CreateThreadByRtlCreateUserThread((LPTHREAD_START_ROUTINE)UnloadWatcherThread, hUnloadEvent);

}


