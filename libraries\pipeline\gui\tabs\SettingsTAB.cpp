﻿#include "pipeline/gui/tabs/SettingsTAB.h"
#include <imgui/imgui.h>
#include "pipeline/gui/GUITheme.h" 
#include "pipeline/settings.h"
#include "main.h"
#include <iostream>

void SettingsTAB::Render()
{
    if (ImGui::BeginTabItem("Settings")) {

        ImGui::Spacing();

        ImGui::TextColored(ImVec4(1.0f, 1.0f, 0.0f, 1.0f), "Version: 1.0.0");
        ImGui::Spacing();

        ImGui::PushTextWrapPos(ImGui::GetCursorPos().x + 550.0f);
        ImGui::Text("Il2CppInspectorPro by Jadis0x");
        ImGui::Text("Discord: Jadis0x");
        ImGui::Text("Github: Jadis0x");
        ImGui::PopTextWrapPos();
        ImGui::Spacing();
        ImGui::Separator();
        ImGui::Spacing();

        ImGui::Checkbox("Show Unity Logs", &settings.bEnableUnityLogs); // Test checkbox
        ImGui::Spacing();

        if (ImGui::Button("Unhook"))
        {
            SetEvent(hUnloadEvent);
        }

        // 增加END和DEL快捷键卸载（新版ImGui API）
        if (ImGui::IsKeyPressed(ImGuiKey_End) || ImGui::IsKeyPressed(ImGuiKey_Delete)) {
            SetEvent(hUnloadEvent);
        }
        ImGui::EndTabItem();
    }
}
