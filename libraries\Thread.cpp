
#include <Thread.h>
#include <mutex>
#include <iostream>

std::vector<std::function<void()>> g_vLambdas = std::vector<std::function<void()>>();
std::mutex g_lambdaMutex;

void Gamethread::execute(const std::function<void()> &lambda) {
    try {
        std::lock_guard<std::mutex> lock(g_lambdaMutex);
        g_vLambdas.push_back(lambda);
    }
    catch (const std::exception& e) {
        std::cerr << "[ERROR] Failed to add lambda to queue: " << e.what() << std::endl;
    }
    catch (...) {
        std::cerr << "[ERROR] Unknown error adding lambda to queue" << std::endl;
    }
}

void Gamethread::_processQueue() {
    try {
        std::vector<std::function<void()>> localQueue;

        // 复制队列并清空原队列（减少锁定时间）
        {
            std::lock_guard<std::mutex> lock(g_lambdaMutex);
            if (g_vLambdas.empty()) {
                return;
            }
            localQueue = std::move(g_vLambdas);
            g_vLambdas.clear();
        }

        // 处理本地队列（不需要锁定）
        for (auto& lambda : localQueue) {
            if (lambda) {
                try {
                    lambda();
                } catch (const std::exception& e) {
                    std::cerr << "[ERROR] Exception in game thread lambda: " << e.what() << std::endl;
                } catch (...) {
                    std::cerr << "[ERROR] Unknown exception in game thread lambda" << std::endl;
                }
            }
        }
    }
    catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in _processQueue: " << e.what() << std::endl;
    }
    catch (...) {
        std::cerr << "[ERROR] Unknown exception in _processQueue" << std::endl;
    }
}
