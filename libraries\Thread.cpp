
#include <Thread.h>


std::vector<std::function<void()>> g_vLambdas = std::vector<std::function<void()>>();

void Gamethread::execute(const std::function<void()> &lambda) {
    g_vLambdas.push_back(lambda);
}

void Gamethread::_processQueue() {
    // Process all queued lambdas
    for (auto& lambda : g_vLambdas) {
        if (lambda) {
            try {
                lambda();
            } catch (const std::exception& e) {
                // Handle any exceptions from lambda execution
                // You might want to log this in a real implementation
                std::cerr << "Exception in game thread lambda: " << e.what() << std::endl;
            }
        }
    }

    // Clear the queue after processing
    g_vLambdas.clear();
}
