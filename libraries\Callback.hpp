﻿#pragma once

#include <vector>
#include <cstdint>
#include <initializer_list>
#include <Windows.h>
#include "VTable.hpp"
#include "Helper.hpp"
#include "UnityResolve.hpp"

namespace UnityResolveLib
{
	struct CallbackHook_t
	{
		std::vector<void*> m_Funcs;

		void** m_VFunc = nullptr;
		void* m_Original = nullptr;
	};

	namespace Callback
	{
		namespace OnUpdate
		{
			inline CallbackHook_t m_CallbackHook;

			inline void Add(void* m_pFunction)
			{
				m_CallbackHook.m_Funcs.emplace_back(m_pFunction);
			}

			inline void __fastcall Hook(void* rcx)
			{
				for (void* m_Func : m_CallbackHook.m_Funcs)
					reinterpret_cast<void(*)()>(m_Func)();

				reinterpret_cast<void(__fastcall*)(void*)>(m_CallbackHook.m_Original)(rcx);
			}
		}

		namespace OnLateUpdate
		{
			inline CallbackHook_t m_CallbackHook;

			inline void Add(void* m_pFunction)
			{
				m_CallbackHook.m_Funcs.emplace_back(m_pFunction);
			}

			inline void __fastcall Hook(void* rcx)
			{
				for (void* m_Func : m_CallbackHook.m_Funcs)
					reinterpret_cast<void(*)()>(m_Func)();

				reinterpret_cast<void(__fastcall*)(void*)>(m_CallbackHook.m_Original)(rcx);
			}
		}

		inline void Initialize()
		{
			// 使用UnityResolve的线程管理
			UnityResolve::ThreadAttach();

			// Find
			auto monoBehaviour = Helper::GetMonoBehaviour();
			void** m_MonoBehaviourVTable = nullptr;
			if (monoBehaviour) {
				// 获取MonoBehaviour的虚函数表
				m_MonoBehaviourVTable = *reinterpret_cast<void***>(monoBehaviour);
			}
			if (m_MonoBehaviourVTable)
			{
				#ifdef _WIN64
					OnUpdate::m_CallbackHook.m_VFunc = Utils::VTable::FindFunction(m_MonoBehaviourVTable, 99, { 0x33, 0xD2, 0xE9 });
					OnLateUpdate::m_CallbackHook.m_VFunc = Utils::VTable::FindFunction(m_MonoBehaviourVTable, 99, { 0xBA, 0x01, 0x00, 0x00, 0x00, 0xE9 });
				#elif _WIN32
					OnUpdate::m_CallbackHook.m_VFunc = Utils::VTable::FindFunction(m_MonoBehaviourVTable, 99, { 0x6A, 0x00, 0xE8 });
					OnLateUpdate::m_CallbackHook.m_VFunc = Utils::VTable::FindFunction(m_MonoBehaviourVTable, 99, { 0x6A, 0x01, 0xE8 });
				#endif
			}

			UnityResolve::ThreadDetach();

			// Replace (Hook) - 添加安全检查
			if (OnUpdate::m_CallbackHook.m_VFunc) {
				Utils::VTable::ReplaceFunction(OnUpdate::m_CallbackHook.m_VFunc, (void*)OnUpdate::Hook, &OnUpdate::m_CallbackHook.m_Original);
			}
			if (OnLateUpdate::m_CallbackHook.m_VFunc) {
				Utils::VTable::ReplaceFunction(OnLateUpdate::m_CallbackHook.m_VFunc, (void*)OnLateUpdate::Hook, &OnLateUpdate::m_CallbackHook.m_Original);
			}
		}

		inline void Uninitialize()
		{
			if (OnUpdate::m_CallbackHook.m_VFunc && OnUpdate::m_CallbackHook.m_Original) {
				Utils::VTable::ReplaceFunction(OnUpdate::m_CallbackHook.m_VFunc, OnUpdate::m_CallbackHook.m_Original);
			}
			if (OnLateUpdate::m_CallbackHook.m_VFunc && OnLateUpdate::m_CallbackHook.m_Original) {
				Utils::VTable::ReplaceFunction(OnLateUpdate::m_CallbackHook.m_VFunc, OnLateUpdate::m_CallbackHook.m_Original);
			}
		}
	}
}