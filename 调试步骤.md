# Unity6.2 崩溃调试步骤

## 当前状态
已创建一个最小化的安全版本，禁用了可能导致崩溃的组件：
- ✅ 基本的 DLL 注入
- ✅ 控制台初始化  
- ✅ UnityResolve 基本初始化
- ❌ Detours 钩子（已禁用）
- ❌ Callback 系统（已禁用）
- ❌ Unity 应用信息获取（已禁用）

## 逐步调试方法

### 第1步：测试基本注入
1. 编译当前版本
2. 注入到目标游戏
3. 检查控制台是否显示：
   ```
   [INFO] Starting initialization...
   [INFO] Waiting for game to fully load...
   [INFO] GameAssembly.dll found at: [地址]
   [INFO] UnityResolve initialized successfully
   [INFO] Successfully found UnityEngine.CoreModule.dll
   ```

### 第2步：如果第1步成功，启用Unity应用信息
在 `main.cpp` 中取消注释：
```cpp
// 取消注释这些行
std::string productName = UApplication::ProductName();
std::string gameVersion = UApplication::GetVersion();
std::string unityVersion = UApplication::GetUnityVersion();
```

### 第3步：如果第2步成功，启用Detours
取消注释：
```cpp
DetourInitilization();
```

### 第4步：如果第3步成功，启用Callback系统
取消注释：
```cpp
UnityResolveLib::Callback::Initialize();
```

## 常见崩溃原因和解决方案

### 1. 立即崩溃（注入时）
**可能原因**：
- 目标游戏有反调试保护
- DLL 架构不匹配（x86 vs x64）
- 权限不足

**解决方案**：
- 确保以管理员身份运行
- 检查目标游戏架构
- 尝试不同的注入器

### 2. UnityResolve 初始化失败
**可能原因**：
- 游戏不是 IL2CPP 构建
- Unity 版本不兼容
- GameAssembly.dll 未完全加载

**解决方案**：
- 增加等待时间（修改 Sleep 值）
- 检查游戏是否使用 Mono 而不是 IL2CPP
- 尝试在游戏完全启动后再注入

### 3. Detours 钩子崩溃
**可能原因**：
- DirectX 版本不匹配
- 游戏使用不同的渲染 API
- 钩子地址错误

**解决方案**：
- 检查游戏使用的渲染 API（DX11/DX12/Vulkan）
- 修改钩子目标函数
- 添加更多的地址验证

### 4. Callback 系统崩溃
**可能原因**：
- MonoBehaviour 虚函数表结构变化
- Unity 版本差异
- 线程安全问题

**解决方案**：
- 更新虚函数表偏移
- 添加版本检测
- 改进线程同步

## 调试工具

### Visual Studio 调试器
1. 编译 Debug 版本
2. 附加到目标进程
3. 在可能崩溃的地方设置断点

### 日志分析
检查以下日志：
- 控制台输出
- `Logs.txt` 文件
- Windows 事件查看器

### 内存分析
使用工具：
- Application Verifier
- CRT Debug Heap
- Valgrind（Linux）

## 安全注入建议

1. **延迟注入**：等待游戏完全启动
2. **分步初始化**：逐个启用功能
3. **异常处理**：包装所有 Unity API 调用
4. **版本检测**：检查 Unity 版本兼容性
5. **权限检查**：确保有足够的访问权限

## 如果仍然崩溃

1. 尝试不同的 Unity 游戏进行测试
2. 检查是否有反作弊系统干扰
3. 使用更简单的注入方法
4. 考虑使用外部工具而不是内存注入

## 联系支持

如果问题持续存在，请提供：
- 目标游戏名称和版本
- Unity 版本信息
- 完整的崩溃日志
- 系统环境信息
