#pragma once

// 功能开关配置
// 通过修改这些宏来启用/禁用特定功能进行测试

// 阶段1：基础功能（始终启用）
#define ENABLE_BASIC_INJECTION 1
#define ENABLE_CONSOLE 1

// 阶段2：UnityResolve 基础功能
#define ENABLE_UNITY_RESOLVE 1
#define ENABLE_ASSEMBLY_INFO 1

// 阶段3：Unity 应用信息
#define ENABLE_UNITY_APP_INFO 0  // 设置为1启用

// 阶段4：GameObject 访问
#define ENABLE_GAMEOBJECT_ACCESS 0  // 设置为1启用

// 阶段5：FindObjectsByType
#define ENABLE_FIND_OBJECTS 0  // 设置为1启用

// 阶段6：Detours 钩子
#define ENABLE_DETOURS_HOOKS 0  // 设置为1启用

// 阶段7：Callback 系统
#define ENABLE_CALLBACK_SYSTEM 0  // 设置为1启用

// 阶段8：ESP 功能
#define ENABLE_ESP_SYSTEM 0  // 设置为1启用

// 调试选项
#define ENABLE_VERBOSE_LOGGING 1
#define ENABLE_EXCEPTION_DETAILS 1
#define STARTUP_DELAY_MS 5000  // 启动延迟（毫秒）

// 安全选项
#define MAX_GAMEOBJECTS_TO_CHECK 10
#define THREAD_SAFETY_CHECKS 1
#define MEMORY_VALIDATION 1
