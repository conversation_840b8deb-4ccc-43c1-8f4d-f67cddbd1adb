#pragma once
#include "UnityResolve.hpp"
#include <iostream>

namespace UnityResolveLib
{
	namespace Helper
	{
		inline UnityResolve::UnityType::Component* GetMonoBehaviour()
			{
				try {
					std::cout << "[DEBUG] GetMonoBehaviour: 开始执行" << std::endl;

					// 添加线程安全检查
					UnityResolve::ThreadAttach();

					// 获取UnityEngine.CoreModule.dll模块
					auto coreModule = UnityResolve::Get("UnityEngine.CoreModule.dll");
					if (!coreModule) {
						std::cout << "[DEBUG] GetMonoBehaviour: coreModule为空" << std::endl;
						UnityResolve::ThreadDetach();
						return nullptr;
					}
					std::cout << "[DEBUG] GetMonoBehaviour: coreModule获取成功" << std::endl;

					// 获取MonoBehaviour类
					auto monoBehaviourClass = coreModule->Get("MonoBehaviour");
					if (!monoBehaviourClass) {
						std::cout << "[DEBUG] GetMonoBehaviour: monoBehaviourClass为空" << std::endl;
						UnityResolve::ThreadDetach();
						return nullptr;
					}
					std::cout << "[DEBUG] GetMonoBehaviour: monoBehaviourClass获取成功" << std::endl;

					// 获取GameObject类
					auto gameObjectClass = coreModule->Get("GameObject");
					if (!gameObjectClass) {
						std::cout << "[DEBUG] GetMonoBehaviour: gameObjectClass为空" << std::endl;
						UnityResolve::ThreadDetach();
						return nullptr;
					}
					std::cout << "[DEBUG] GetMonoBehaviour: gameObjectClass获取成功" << std::endl;

					std::cout << "[DEBUG] GetMonoBehaviour: 准备调用FindObjectsByType" << std::endl;

					// 使用GameObject类的FindObjectsByType方法获取所有GameObject实例
					std::vector<UnityResolve::UnityType::GameObject*> gameObjects;
					try {
						gameObjects = gameObjectClass->FindObjectsByType<UnityResolve::UnityType::GameObject*>();
						std::cout << "[DEBUG] GetMonoBehaviour: FindObjectsByType调用完成，gameObjects数量: " << gameObjects.size() << std::endl;
					}
					catch (const std::exception& e) {
						std::cout << "[ERROR] GetMonoBehaviour: FindObjectsByType异常: " << e.what() << std::endl;
						UnityResolve::ThreadDetach();
						return nullptr;
					}
					catch (...) {
						std::cout << "[ERROR] GetMonoBehaviour: FindObjectsByType未知异常" << std::endl;
						UnityResolve::ThreadDetach();
						return nullptr;
					}

					if (gameObjects.empty()) {
						std::cout << "[DEBUG] GetMonoBehaviour: gameObjects为空" << std::endl;
						UnityResolve::ThreadDetach();
						return nullptr;
					}

					// 限制检查的对象数量以避免长时间阻塞
					size_t maxCheck = std::min(gameObjects.size(), static_cast<size_t>(10));

					for (size_t index = 0; index < maxCheck; ++index)
					{
						auto gameObject = gameObjects[index];
						std::cout << "[DEBUG] GetMonoBehaviour: 处理gameObject[" << index << "]" << std::endl;

						if (!gameObject) {
							std::cout << "[DEBUG] GetMonoBehaviour: gameObject[" << index << "]为空，跳过" << std::endl;
							continue;
						}

						try {
							std::cout << "[DEBUG] GetMonoBehaviour: 准备调用GetComponent[" << index << "]" << std::endl;
							// 使用GameObject的GetComponent方法获取MonoBehaviour组件
							auto monoBehaviour = gameObject->template GetComponent<UnityResolve::UnityType::Component*>(monoBehaviourClass);
							std::cout << "[DEBUG] GetMonoBehaviour: GetComponent[" << index << "]调用完成" << std::endl;
							if (monoBehaviour) {
								std::cout << "[DEBUG] GetMonoBehaviour: 找到MonoBehaviour，返回" << std::endl;
								UnityResolve::ThreadDetach();
								return monoBehaviour;
							}
						}
						catch (const std::exception& e) {
							std::cout << "[DEBUG] GetMonoBehaviour: GetComponent[" << index << "]异常: " << e.what() << std::endl;
							continue;
						}
						catch (...) {
							std::cout << "[DEBUG] GetMonoBehaviour: GetComponent[" << index << "]未知异常，继续下一个" << std::endl;
							continue;
						}
					}
					std::cout << "[DEBUG] GetMonoBehaviour: 遍历完成，未找到MonoBehaviour" << std::endl;
					UnityResolve::ThreadDetach();
				}
				catch (const std::exception& e) {
					std::cout << "[ERROR] GetMonoBehaviour: 捕获异常: " << e.what() << std::endl;
					UnityResolve::ThreadDetach();
					return nullptr;
				}
				catch (...) {
					std::cout << "[ERROR] GetMonoBehaviour: 捕获未知异常，返回nullptr" << std::endl;
					UnityResolve::ThreadDetach();
					return nullptr;
				}

				return nullptr;
			}
	}
}