#define WIN32_LEAN_AND_MEAN
#include <Windows.h>
#include <iostream>
#include <mutex>
#include "helpers.h"
#include "main.h"
#include <tlhelp32.h>

extern const LPCWSTR LOG_FILE = L"Logs.txt";

HMODULE hModule;
HANDLE hUnloadEvent;

typedef LONG NTSTATUS;
typedef NTSTATUS(WINAPI* pNtCreateThreadEx)(PHANDLE ThreadHandle,ACCESS_MASK DesiredAccess,LPVOID ObjectAttributes,HAND<PERSON> ProcessHandle,LPTHREAD_START_ROUTINE lpStartAddress,LPVOID lpParameter,BOOL CreateSuspended,DWOR<PERSON> StackZeroBits,DWORD SizeOfStackCommit,DWOR<PERSON> SizeOfStackReserve,<PERSON>VO<PERSON> lpBytesBuffer);

typedef struct _CLIENT_ID {
    HANDLE UniqueProcess;
    HANDLE UniqueThread;
} CLIENT_ID, * PCLIENT_ID;

typedef NTSTATUS(WINAPI* pRtlCreateUserThread)(<PERSON><PERSON><PERSON><PERSON> ProcessHandle,PSECURITY_DESCRIPTOR SecurityDescriptor,BOOLEAN CreateSuspended,ULONG StackZeroBits,PULONG StackReserved,PULONG StackCommit,PVOID StartAddress,PVOID StartParameter,PHANDLE ThreadHandle,PCLIENT_ID ClientID);

void CreateThreadByNtCreateThreadEx(LPTHREAD_START_ROUTINE func, LPVOID param) {
    HMODULE hNtdll = GetModuleHandleA("ntdll.dll");
    if (!hNtdll) return;
    pNtCreateThreadEx NtCreateThreadEx = (pNtCreateThreadEx)GetProcAddress(hNtdll, "NtCreateThreadEx");
    if (!NtCreateThreadEx) return;
    HANDLE hThread = NULL;
    NTSTATUS status = NtCreateThreadEx(&hThread, 0x1FFFFF, NULL, GetCurrentProcess(), func, param, FALSE, 0, 0, 0, NULL);
    if (hThread) CloseHandle(hThread);
}

void CreateThreadByRtlCreateUserThread(LPTHREAD_START_ROUTINE func, LPVOID param) {
    HMODULE hNtdll = GetModuleHandleA("ntdll.dll");
    if (!hNtdll) return;
    pRtlCreateUserThread RtlCreateUserThread = (pRtlCreateUserThread)GetProcAddress(hNtdll, "RtlCreateUserThread");
    if (!RtlCreateUserThread) return;
    HANDLE hThread = NULL;
    CLIENT_ID cid;
    NTSTATUS status = RtlCreateUserThread(GetCurrentProcess(), NULL, FALSE, 0, NULL, NULL, func, param, &hThread, &cid);
    if (hThread) CloseHandle(hThread);
}

DWORD WINAPI UnloadWatcherThread(LPVOID lpParam)
{
    HANDLE hEvent = static_cast<HANDLE>(lpParam);

    if (WaitForSingleObject(hEvent, INFINITE) == WAIT_OBJECT_0)
    {
        std::cout << "[WARNING] Unload signal received.." << std::endl;
        fclose(stdout);
        FreeConsole();
        CloseHandle(hUnloadEvent);
        FreeLibraryAndExitThread(hModule, 0);
    }
    return 0;
}

// 最小化的安全运行函数
void Run(LPVOID lpParam)
{
    try {
        std::cout << "[INFO] Safe version starting..." << std::endl;
        
        hModule = (HMODULE)lpParam;
        SetConsole();
        
        std::cout << "[INFO] Console initialized successfully" << std::endl;
        
        // 等待一段时间确保游戏完全加载
        std::cout << "[INFO] Waiting for game to stabilize..." << std::endl;
        Sleep(5000); // 等待5秒
        
        // 检查 GameAssembly.dll 是否存在
        HMODULE gameAssembly = GetModuleHandleA("GameAssembly.dll");
        if (!gameAssembly) {
            std::cout << "[ERROR] GameAssembly.dll not found!" << std::endl;
            std::cout << "[INFO] This might not be a Unity IL2CPP game" << std::endl;
        } else {
            std::cout << "[INFO] GameAssembly.dll found at: " << gameAssembly << std::endl;
        }
        
        // 检查其他常见的Unity DLL
        HMODULE unityPlayer = GetModuleHandleA("UnityPlayer.dll");
        if (unityPlayer) {
            std::cout << "[INFO] UnityPlayer.dll found at: " << unityPlayer << std::endl;
        }
        
        HMODULE mono = GetModuleHandleA("mono.dll");
        if (mono) {
            std::cout << "[INFO] mono.dll found - this might be a Mono game" << std::endl;
        }
        
        std::cout << "[INFO] Basic initialization completed successfully!" << std::endl;
        std::cout << "[INFO] DLL injection is working properly" << std::endl;
        std::cout << "[INFO] You can now gradually enable more features" << std::endl;

        // 创建卸载事件
        hUnloadEvent = CreateEvent(NULL, FALSE, FALSE, NULL);
        if (hUnloadEvent == NULL) {
            std::cout << "[ERROR] Failed to create unload event! Error code: " << GetLastError() << std::endl;
            return;
        }

        std::cout << "[INFO] Creating unload watcher thread..." << std::endl;
        CreateThreadByRtlCreateUserThread((LPTHREAD_START_ROUTINE)UnloadWatcherThread, hUnloadEvent);
        
        std::cout << "[INFO] Safe initialization completed successfully!" << std::endl;
        std::cout << "[INFO] Check the console for any error messages" << std::endl;
    }
    catch (const std::exception& e) {
        std::cout << "[FATAL ERROR] Safe Run function exception: " << e.what() << std::endl;
    }
    catch (...) {
        std::cout << "[FATAL ERROR] Safe Run function unknown exception" << std::endl;
    }
}
