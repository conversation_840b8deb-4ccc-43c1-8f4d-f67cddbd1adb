chcp 65001
@echo off
REM 自动清理 Visual Studio 项目中间文件和临时文件

setlocal

REM 删除常见中间文件夹
for %%d in (Debug Release x64 x86 ipch .vs) do (
    if exist %%d (
        echo 正在删除文件夹 %%d ...
        rmdir /s /q %%d
    )
)

REM 删除所有 *.obj、*.pdb、*.idb、*.ipdb、*.iobj、*.tlog、*.log 文件
for /r %%i in (*.obj *.pdb *.idb *.ipdb *.iobj *.tlog *.log) do del /f /q "%%i"

REM 删除所有 *.sdf、*.opensdf、*.VC.db 文件
for %%f in (*.sdf *.opensdf *.VC.db) do del /f /q "%%f"

echo 清理完成。
pause
endlocal 