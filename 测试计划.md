# Unity6.2 逐步测试计划

## 当前状态：✅ 基础版本稳定

基础 DLL 注入已经成功，不再崩溃。现在我们需要逐步添加功能来确定之前崩溃的具体原因。

## 测试阶段

### 阶段1：✅ 基础注入测试（已完成）
- [x] DLL 成功注入
- [x] 控制台正常显示
- [x] 基本线程创建正常
- [x] 卸载机制正常

**结果**: 成功，无崩溃

### 阶段2：🔄 UnityResolve 基础功能测试（当前）
**目标**: 测试 UnityResolve 初始化和基本功能

**测试内容**:
- [ ] UnityResolve::Init() 调用
- [ ] 获取 UnityEngine.CoreModule.dll
- [ ] 显示程序集信息
- [ ] 显示类数量

**预期输出**:
```
[INFO] UnityResolve initialized successfully!
[SUCCESS] Found UnityEngine.CoreModule.dll
[INFO] Assembly name: UnityEngine.CoreModule
[INFO] Classes count: [数字]
```

**如果失败**: 说明 UnityResolve 初始化有问题

### 阶段3：Unity 应用信息测试
**目标**: 测试获取 Unity 应用基本信息

**添加代码**:
```cpp
std::string productName = UApplication::ProductName();
std::string gameVersion = UApplication::GetVersion();
std::string unityVersion = UApplication::GetUnityVersion();
```

**如果失败**: 说明 UApplication 类访问有问题

### 阶段4：基础 GameObject 访问测试
**目标**: 测试访问 GameObject 类

**添加代码**:
```cpp
auto gameObjectClass = coreModule->Get("GameObject");
if (gameObjectClass) {
    std::cout << "[SUCCESS] Found GameObject class" << std::endl;
}
```

**如果失败**: 说明类查找有问题

### 阶段5：FindObjectsByType 测试
**目标**: 测试查找游戏对象

**添加代码**:
```cpp
auto gameObjects = gameObjectClass->FindObjectsByType<UnityResolve::UnityType::GameObject*>();
std::cout << "[INFO] Found " << gameObjects.size() << " GameObjects" << std::endl;
```

**如果失败**: 说明 FindObjectsByType 有问题

### 阶段6：Detours 钩子测试
**目标**: 测试 DirectX 钩子

**启用文件**:
- `libraries\pipeline\hooks\InitHooks.cpp`
- `libraries\pipeline\hooks\DirectX.cpp`

**如果失败**: 说明 DirectX 钩子有问题

### 阶段7：Callback 系统测试
**目标**: 测试 MonoBehaviour 回调

**启用代码**:
```cpp
UnityResolveLib::Callback::Initialize();
```

**如果失败**: 说明 Callback 系统有问题

### 阶段8：ESP 功能测试
**目标**: 测试完整的 ESP 功能

**启用文件**:
- `user\Esp.cpp`

**如果失败**: 说明 ESP 渲染有问题

## 测试方法

### 每个阶段的测试步骤：
1. 修改代码添加新功能
2. 编译项目
3. 注入到目标游戏
4. 观察控制台输出
5. 检查是否崩溃

### 如果某阶段崩溃：
1. 立即回退到上一个稳定版本
2. 记录崩溃时的具体操作
3. 分析崩溃原因
4. 修复问题后重新测试

## 崩溃分析工具

### Visual Studio 调试器
1. 编译 Debug 版本
2. 附加到目标进程
3. 设置断点在新添加的代码处

### 日志分析
- 控制台输出
- `Logs.txt` 文件
- Windows 事件查看器

### 内存分析
- 使用 Application Verifier
- 检查访问违规地址
- 分析调用堆栈

## 预期问题和解决方案

### UnityResolve 初始化失败
**可能原因**:
- Unity 版本不兼容
- IL2CPP 元数据格式变化
- 游戏使用了代码混淆

**解决方案**:
- 检查 Unity 版本
- 尝试不同的初始化参数
- 添加版本检测

### DirectX 钩子崩溃
**可能原因**:
- 游戏使用 DX12 或 Vulkan
- 钩子地址错误
- 反作弊检测

**解决方案**:
- 检测渲染 API
- 更新钩子地址
- 添加反检测措施

### Callback 系统崩溃
**可能原因**:
- MonoBehaviour 虚函数表变化
- 线程安全问题
- Unity 版本差异

**解决方案**:
- 更新虚函数表偏移
- 改进线程同步
- 添加版本适配

## 成功标准

每个阶段成功的标准：
- ✅ 无崩溃
- ✅ 控制台显示预期输出
- ✅ 功能正常工作
- ✅ 无内存泄漏

## 最终目标

完成所有阶段后，我们应该有：
1. 稳定的 DLL 注入
2. 正常工作的 UnityResolve
3. 功能完整的 ESP 系统
4. 可靠的错误处理机制
