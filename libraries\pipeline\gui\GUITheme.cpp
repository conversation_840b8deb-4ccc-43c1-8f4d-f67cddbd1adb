﻿#include "pipeline/gui/GUITheme.h"
#include "pipeline/settings.h"
#include <imgui/imgui.h>
#include <imgui/imgui_internal.h>

void ApplyTheme()
{
    ImGui::CreateContext();
    ImGuiIO& io = ImGui::GetIO();

	auto& Style = ImGui::GetStyle();
	auto& Colors = Style.Colors;


	Style.WindowRounding = 0.0f;
	Style.FrameRounding = 0.0f;
	Style.GrabRounding = 0.0f;
	Style.ScrollbarRounding = 0.0f;
	Style.TabRounding = 0.0f;

	Style.FramePadding = ImVec2(8.0f, 6.0f);
	Style.ItemSpacing = ImVec2(8.0f, 6.0f);

	Colors[ImGuiCol_Text] = ImColor(255, 255, 255, 255);
	Colors[ImGuiCol_TextDisabled] = ImColor(128, 128, 128, 255);
	Colors[ImGuiCol_WindowBg] = ImColor(0, 0, 0, 200);
	Colors[ImGuiCol_ChildBg] = ImColor(0, 0, 0, 0);
	Colors[ImGuiCol_PopupBg] = ImColor(0, 0, 0, 220);
	Colors[ImGuiCol_Border] = ImColor(255, 255, 255, 30);
	Colors[ImGuiCol_BorderShadow] = ImColor(0, 0, 0, 0);
	Colors[ImGuiCol_FrameBg] = ImColor(64, 64, 64, 255);
	Colors[ImGuiCol_FrameBgHovered] = ImColor(80, 80, 80, 255);
	Colors[ImGuiCol_FrameBgActive] = ImColor(96, 96, 96, 255);
	Colors[ImGuiCol_TitleBg] = ImColor(0, 0, 0, 180);
	Colors[ImGuiCol_TitleBgActive] = ImColor(0, 0, 0, 220);
	Colors[ImGuiCol_TitleBgCollapsed] = ImColor(0, 0, 0, 160);
	Colors[ImGuiCol_MenuBarBg] = ImColor(0, 0, 0, 170);
	Colors[ImGuiCol_ScrollbarBg] = ImColor(0, 0, 0, 150);
	Colors[ImGuiCol_ScrollbarGrab] = ImColor(255, 255, 255, 50);
	Colors[ImGuiCol_ScrollbarGrabHovered] = ImColor(255, 255, 255, 75);
	Colors[ImGuiCol_ScrollbarGrabActive] = ImColor(255, 255, 255, 100);
	Colors[ImGuiCol_CheckMark] = ImColor(128, 128, 128, 255);
	Colors[ImGuiCol_SliderGrab] = ImColor(255, 255, 255, 150);
	Colors[ImGuiCol_SliderGrabActive] = ImColor(255, 255, 255, 200);
	Colors[ImGuiCol_Button] = ImColor(128, 0, 128, 255);
	Colors[ImGuiCol_ButtonHovered] = ImColor(147, 112, 219, 255);
	Colors[ImGuiCol_ButtonActive] = ImColor(186, 85, 211, 255);
	Colors[ImGuiCol_Header] = ImColor(0, 0, 0, 180);
	Colors[ImGuiCol_HeaderHovered] = ImColor(255, 255, 255, 20);
	Colors[ImGuiCol_HeaderActive] = ImColor(255, 255, 255, 40);
	Colors[ImGuiCol_Separator] = ImColor(255, 255, 255, 30);
	Colors[ImGuiCol_SeparatorHovered] = ImColor(255, 255, 255, 50);
	Colors[ImGuiCol_SeparatorActive] = ImColor(255, 255, 255, 70);
	Colors[ImGuiCol_ResizeGrip] = ImColor(255, 255, 255, 20);
	Colors[ImGuiCol_ResizeGripHovered] = ImColor(255, 255, 255, 40);
	Colors[ImGuiCol_ResizeGripActive] = ImColor(255, 255, 255, 60);
	Colors[ImGuiCol_Tab] = ImColor(0, 0, 0, 150);
	Colors[ImGuiCol_TabHovered] = ImColor(255, 255, 255, 20);
	Colors[ImGuiCol_TabActive] = ImColor(128, 0, 128, 255);
	Colors[ImGuiCol_TabUnfocused] = ImColor(0, 0, 0, 100);
	Colors[ImGuiCol_TabUnfocusedActive] = ImColor(0, 0, 0, 120);
	Colors[ImGuiCol_PlotLines] = ImColor(255, 255, 255, 150);
	Colors[ImGuiCol_PlotLinesHovered] = ImColor(255, 255, 255, 200);
	Colors[ImGuiCol_PlotHistogram] = ImColor(255, 255, 255, 150);
	Colors[ImGuiCol_PlotHistogramHovered] = ImColor(255, 255, 255, 200);
	Colors[ImGuiCol_TextSelectedBg] = ImColor(255, 255, 255, 50);
	Colors[ImGuiCol_DragDropTarget] = ImColor(255, 255, 0, 200);
	Colors[ImGuiCol_NavHighlight] = ImColor(255, 255, 255, 100);
	Colors[ImGuiCol_NavWindowingHighlight] = ImColor(255, 255, 255, 100);
	Colors[ImGuiCol_NavWindowingDimBg] = ImColor(0, 0, 0, 50);
	Colors[ImGuiCol_ModalWindowDimBg] = ImColor(0, 0, 0, 75);

	ImGui::PushStyleVar(ImGuiStyleVar_FramePadding, ImVec2(10.0f, 8.0f));


}
